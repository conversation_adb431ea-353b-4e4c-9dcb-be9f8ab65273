{"name": "eces-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vue-tsc -b && vite build", "preview": "vite preview  --host"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.11.0", "element-plus": "^2.10.5", "vue": "^3.5.17"}, "devDependencies": {"@types/node": "^24.1.0", "@vitejs/plugin-vue": "^6.0.0", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^7.0.4", "vue-tsc": "^2.2.12"}}