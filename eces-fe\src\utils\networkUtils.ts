// 网络状态检测工具
import { ElMessage, ElNotification } from 'element-plus'

// 网络状态检测类
export class NetworkMonitor {
  private static instance: NetworkMonitor
  private isOnline: boolean = navigator.onLine
  private listeners: Array<(isOnline: boolean) => void> = []

  private constructor() {
    this.setupEventListeners()
  }

  public static getInstance(): NetworkMonitor {
    if (!NetworkMonitor.instance) {
      NetworkMonitor.instance = new NetworkMonitor()
    }
    return NetworkMonitor.instance
  }

  private setupEventListeners() {
    window.addEventListener('online', () => {
      this.isOnline = true
      this.notifyListeners(true)
      ElNotification({
        title: '网络连接已恢复',
        message: '您的网络连接已恢复正常',
        type: 'success',
        duration: 3000
      })
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
      this.notifyListeners(false)
      ElNotification({
        title: '网络连接已断开',
        message: '请检查您的网络连接',
        type: 'error',
        duration: 0 // 不自动关闭
      })
    })
  }

  private notifyListeners(isOnline: boolean) {
    this.listeners.forEach(listener => listener(isOnline))
  }

  public addListener(listener: (isOnline: boolean) => void) {
    this.listeners.push(listener)
  }

  public removeListener(listener: (isOnline: boolean) => void) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  public getNetworkStatus(): boolean {
    return this.isOnline
  }

  // 检测网络连接质量
  public async checkNetworkQuality(baseUrl: string): Promise<{
    isReachable: boolean
    responseTime: number
    error?: string
  }> {
    const startTime = Date.now()
    
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时

      const response = await fetch(`${baseUrl}health`, {
        method: 'GET',
        signal: controller.signal,
        cache: 'no-cache'
      })

      clearTimeout(timeoutId)
      const responseTime = Date.now() - startTime

      return {
        isReachable: response.ok,
        responseTime,
        error: response.ok ? undefined : `HTTP ${response.status}: ${response.statusText}`
      }
    } catch (error: any) {
      const responseTime = Date.now() - startTime
      
      let errorMessage = '网络连接失败'
      if (error.name === 'AbortError') {
        errorMessage = '连接超时'
      } else if (error.message) {
        errorMessage = error.message
      }

      return {
        isReachable: false,
        responseTime,
        error: errorMessage
      }
    }
  }

  // 显示网络状态提示
  public showNetworkStatus(baseUrl: string) {
    if (!this.isOnline) {
      ElMessage.error('网络连接已断开，请检查网络设置')
      return
    }

    this.checkNetworkQuality(baseUrl).then(result => {
      if (result.isReachable) {
        ElMessage.success(`服务器连接正常 (响应时间: ${result.responseTime}ms)`)
      } else {
        ElMessage.error(`服务器连接失败: ${result.error}`)
      }
    })
  }
}

// 网络重试工具
export class NetworkRetry {
  public static async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000,
    backoff: boolean = true
  ): Promise<T> {
    let lastError: any

    for (let attempt = 0; attempt <= maxRetries; attempt++) {
      try {
        return await operation()
      } catch (error: any) {
        lastError = error
        
        // 如果是最后一次尝试，直接抛出错误
        if (attempt === maxRetries) {
          break
        }

        // 检查是否应该重试
        if (!this.shouldRetry(error)) {
          break
        }

        // 计算延迟时间
        const currentDelay = backoff ? delay * Math.pow(2, attempt) : delay
        await this.sleep(currentDelay)
      }
    }

    throw lastError
  }

  private static shouldRetry(error: any): boolean {
    // 网络错误或服务器错误时重试
    if (error.code === 'ECONNABORTED' || error.code === 'ERR_NETWORK') {
      return true
    }
    
    if (error.response && error.response.status >= 500) {
      return true
    }

    return false
  }

  private static sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

// 导出单例实例
export const networkMonitor = NetworkMonitor.getInstance()
