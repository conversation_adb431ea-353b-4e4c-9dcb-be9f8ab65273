# ECES 增效处理系统

Excel 增效处理服务管理平台

## 功能特性

### 文件管理

- 支持上传数据文件和模板文件（Excel/CSV 格式）
- 实时获取文件列表
- 下载处理结果文件
- 下载配置文件

### 处理控制

- **启动处理**：点击启动处理后，系统会显示美观的转圈等待状态
- **状态查询**：自动轮询处理状态，支持以下情况：
  - **成功**：当接口返回 `"status": "处理完成"` 时，显示成功弹窗，包含开始时间、结束时间等信息
  - **失败**：当接口返回 `"status": "处理失败"` 时，显示失败弹窗，提示具体错误信息
  - **处理中**：当接口返回 `"status": "处理中"` 时，继续轮询状态
- **上次处理状态查询**：点击按钮可查询上次处理的最终状态，支持完成、失败、处理中三种状态
- **实时反馈**：处理过程中显示动态的转圈动画和状态信息

### NG 模块特有功能

- 字典值查询
- 字典信息显示
- 添加字典值

## 技术栈

- Vue 3 + TypeScript
- Element Plus UI 组件库
- Axios HTTP 客户端
- Vite 构建工具

## 开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run preview
```

## 处理状态说明

### 成功响应示例

```json
{
  "message": "ok",
  "data": {
    "status": "处理完成",
    "message": "",
    "startTime": "2025/8/4 10:22:52",
    "endTime": "2025/8/4 10:23:24",
    "untranslate": []
  },
  "code": 200
}
```

### 处理失败响应示例

```json
{
  "message": "ok",
  "data": {
    "status": "处理失败",
    "message": "System.IO.IOException: The process cannot access the file...",
    "startTime": "2025/8/4 10:31:06",
    "endTime": "2025/8/4 10:31:06",
    "untranslate": []
  },
  "code": 200
}
```

### 失败响应示例

```json
{
  "message": "error",
  "data": "具体错误信息",
  "code": 500
}
```

## 界面特性

- 现代化的渐变设计
- 响应式布局，支持移动端
- 美观的转圈等待动画
- 友好的成功/失败弹窗提示
- 实时状态更新
