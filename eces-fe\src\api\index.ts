import axios, { AxiosResponse, AxiosError, AxiosRequestConfig } from 'axios'
import type { ServiceResponse, FileType } from '@/types'

// 重试配置
interface RetryConfig {
  retries: number
  retryDelay: number
  retryCondition?: (error: AxiosError) => boolean
}

// 默认重试配置
const defaultRetryConfig: RetryConfig = {
  retries: 3,
  retryDelay: 1000,
  retryCondition: (error: AxiosError) => {
    // 网络错误或5xx服务器错误时重试
    return !error.response || (error.response.status >= 500 && error.response.status < 600)
  }
}

// 创建axios实例
const createApiInstance = (baseURL: string) => {
  const instance = axios.create({
    baseURL,
    timeout: 120000, // 增加到2分钟，适合大文件上传
    headers: {
      'Content-Type': 'application/json'
    }
  })

  // 请求拦截器
  instance.interceptors.request.use(
    (config) => {
      // 为文件上传请求设置更长的超时时间
      if (config.headers['Content-Type'] === 'multipart/form-data') {
        config.timeout = 300000 // 5分钟
      }
      return config
    },
    (error) => {
      return Promise.reject(error)
    }
  )

  // 响应拦截器
  instance.interceptors.response.use(
    (response) => {
      return response
    },
    async (error: AxiosError) => {
      const config = error.config as AxiosRequestConfig & { _retryCount?: number }

      // 如果没有配置重试信息，使用默认配置
      if (!config._retryCount) {
        config._retryCount = 0
      }

      // 检查是否应该重试
      if (
        config._retryCount < defaultRetryConfig.retries &&
        defaultRetryConfig.retryCondition!(error)
      ) {
        config._retryCount++

        return instance(config)
      }

      return Promise.reject(error)
    }
  )

  return instance
}

// 通用API类
export class ApiService {
  private api: any

  constructor(baseURL: string) {
    this.api = createApiInstance(baseURL)
  }

  // 处理API错误的通用方法
  private handleApiError(error: any, operation: string): never {
    let errorMessage = `${operation}失败`

    if (error.code === 'ECONNABORTED') {
      errorMessage = `${operation}超时，请检查网络连接或稍后重试`
    } else if (error.code === 'ERR_NETWORK') {
      errorMessage = `网络连接失败，请检查网络设置`
    } else if (error.response) {
      // 服务器响应了错误状态码
      const status = error.response.status
      if (status >= 500) {
        errorMessage = `服务器内部错误(${status})，请稍后重试`
      } else if (status === 404) {
        errorMessage = `服务接口不存在(${status})`
      } else if (status === 403) {
        errorMessage = `访问被拒绝(${status})`
      } else {
        errorMessage = `请求失败(${status}): ${error.response.data?.message || error.message}`
      }
    } else if (error.request) {
      // 请求已发出但没有收到响应
      errorMessage = `网络请求无响应，请检查网络连接`
    } else {
      errorMessage = `${operation}失败: ${error.message}`
    }

    console.error(`API Error [${operation}]:`, error)
    throw new Error(errorMessage)
  }

  // 上传文件
  async uploadFile(file: File, fileType: FileType): Promise<ServiceResponse> {
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response: AxiosResponse<ServiceResponse> = await this.api.post(
        `/FileService/uploadfile?fileType=${fileType}`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      )
      return response.data
    } catch (error) {
      this.handleApiError(error, '文件上传')
    }
  }

  // 获取文件列表
  async getFileList(): Promise<ServiceResponse> {
    try {
      const response: AxiosResponse<ServiceResponse> = await this.api.get('/FileService/listfile')
      return response.data
    } catch (error) {
      this.handleApiError(error, '获取文件列表')
    }
  }

  // 下载结果文件
  async downloadResultFile(): Promise<Blob> {
    try {
      const response = await this.api.get('/FileService/downloadfile', {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      this.handleApiError(error, '下载结果文件')
    }
  }

  // 启动处理
  async executeProcess(): Promise<ServiceResponse> {
    try {
      const response: AxiosResponse<ServiceResponse> = await this.api.get('/ExcelProcess/execute')
      return response.data
    } catch (error) {
      this.handleApiError(error, '启动处理')
    }
  }

  // 查询处理状态
  async getProcessStatus(): Promise<ServiceResponse> {
    try {
      const response: AxiosResponse<ServiceResponse> = await this.api.get('/ExcelProcess/status')
      return response.data
    } catch (error) {
      this.handleApiError(error, '查询处理状态')
    }
  }

  // 下载配置文件
  async downloadConfigFile(fileName: string): Promise<Blob> {
    try {
      const response = await this.api.get(`/MaintainService/downloadfile?fileName=${fileName}`, {
        responseType: 'blob'
      })
      return response.data
    } catch (error) {
      this.handleApiError(error, '下载配置文件')
    }
  }

  // NG模块特有方法
  async getDictValue(iniSpace: string, key: string): Promise<ServiceResponse> {
    try {
      const response: AxiosResponse<ServiceResponse> = await this.api.get(
        `/DicService/dicget?iniSpace=${iniSpace}&key=${key}`
      )
      return response.data
    } catch (error) {
      this.handleApiError(error, '获取字典值')
    }
  }

  async getDictInfo(): Promise<ServiceResponse> {
    try {
      const response: AxiosResponse<ServiceResponse> = await this.api.get('/DicService/dicinfo')
      return response.data
    } catch (error) {
      this.handleApiError(error, '获取字典信息')
    }
  }

  async addDictKey(iniSpace: string, key: string, value: string): Promise<ServiceResponse> {
    try {
      const response: AxiosResponse<ServiceResponse> = await this.api.post(
        `/DicService/adddickey?iniSpace=${iniSpace}&key=${key}&value=${value}`
      )
      return response.data
    } catch (error) {
      this.handleApiError(error, '添加字典值')
    }
  }
} 