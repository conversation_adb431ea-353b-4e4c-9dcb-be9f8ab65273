// API响应类型
export interface ServiceResponse {
  message?: string
  data?: any
  code: number
}

// 模块配置类型
export interface ModuleConfig {
  name: string
  title: string
  baseUrl: string
  swagger: any
}

// 文件类型
export type FileType = 'data' | 'template'

// 处理状态
export interface ProcessStatus {
  isProcessing: boolean
  status: string
  progress: number
}

// 处理状态响应
export interface ProcessStatusResponse {
  status: string
  message: string
  startTime: string
  endTime: string
  untranslate: any[]
}

// 文件信息
export interface FileInfo {
  Name: string
  CreateTime: string
  LastWriteTime: string
}

// 文件列表响应
export interface FileListResponse {
  数据文件: FileInfo[]
  模板文件: FileInfo[]
  结果文件: FileInfo[]
}

// 字典项
export interface DictItem {
  key: string
  value: string
}

// 字典信息
export interface DictInfo {
  iniSpace: string
  items: DictItem[]
} 